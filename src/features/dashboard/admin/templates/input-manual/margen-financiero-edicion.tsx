import { TitleSubtitle } from '@c/components/TitleSubtitle'
import { authStore } from '@c/store/auth-store'
import {
  margenFinancieroAjusteMetadata,
  margenFinancieroEdicionMetadata,
  type MargenFinancieroInforme,
  margenFinancieroInforme,
  margenFinancieroInformeDefault
} from '@d/admin/models/margen-financiero-informe'
import { MargenFinancieroRepository } from '@d/admin/repositories/margen-financiero-repository'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Loading } from '@d/common/components/Loading'
import {
  Box,
  Button,
  Checkbox,
  Container,
  FormControlLabel,
  FormGroup,
  Grid,
  LinearProgress,
  OutlinedInput,
  Paper
} from '@mui/material'
import { useMutation } from '@tanstack/react-query'
import type { Dayjs } from 'dayjs'
import { nth } from 'rambdax'
import type { FC } from 'react'

import { alertStore } from '@/common/store/alert-store'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export const MargenFinancieroEdicionTemplate: FC<{
  ajustes: MargenFinancieroInforme[]
  data: MargenFinancieroInforme[]
  date: Dayjs
  isMargenFinancieroAjustesPending: boolean
  refetch: () => void
}> = ({ ajustes, data, date, isMargenFinancieroAjustesPending, refetch }) => {
  const [saldo, setSaldo] = useState('')
  const [interes, setInteres] = useState('')
  const [margen, setMargen] = useState('')
  const [ajusteDia, setAjusteDia] = useState(true)
  const [ajusteMes, setAjusteMes] = useState(true)

  const [selectedRow, setSelectedRow] =
    useState<MargenFinancieroInforme | null>(null)

  const user = authStore.user.use()

  const {
    ajusteMargen,
    aplicaAjusteMargen,
    setAjusteIntereses,
    setAjusteSaldo
  } = container.get(MargenFinancieroRepository)

  const {
    isError: isAjusteSaldoError,
    isPending: isAjusteSaldoPending,
    isSuccess: isAjusteSaldoSuccess,
    mutate: mutateAjusteSaldo
  } = useMutation({
    mutationFn: setAjusteSaldo,
    onSuccess: refetch
  })

  const {
    isError: isAjusteInteresesError,
    isPending: isAjusteInteresesPending,
    isSuccess: isAjusteInteresesSuccess,
    mutate: mutateAjusteIntereses
  } = useMutation({
    mutationFn: setAjusteIntereses,
    onSuccess: refetch
  })

  const {
    isError: isAjusteMargeError,
    isPending: isAjusteMargePending,
    isSuccess: isAjusteMargeSuccess,
    mutate: mutateAjusteMargen
  } = useMutation({
    mutationFn: ajusteMargen,
    onSuccess: refetch
  })

  const {
    isError: isAplicaAjusteMargeError,
    isPending: isAplicaAjusteMargePending,
    isSuccess: isAplicaAjusteMargeSuccess,
    mutate: mutateAplicaAjusteMargen
  } = useMutation({
    mutationFn: aplicaAjusteMargen,
    onSuccess: refetch
  })

  useEffect(() => {
    if (
      isAjusteSaldoSuccess ||
      isAjusteInteresesSuccess ||
      isAjusteMargeSuccess ||
      isAplicaAjusteMargeSuccess
    )
      alertStore.displayInfo()
  }, [
    isAjusteSaldoSuccess,
    isAjusteInteresesSuccess,
    isAjusteMargeSuccess,
    isAplicaAjusteMargeSuccess
  ])

  useEffect(() => {
    if (
      isAjusteSaldoError ||
      isAjusteInteresesError ||
      isAjusteMargeError ||
      isAplicaAjusteMargeError
    )
      alertStore.displayError()
  }, [
    isAjusteSaldoError,
    isAjusteInteresesError,
    isAjusteMargeError,
    isAplicaAjusteMargeError
  ])

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Container sx={{ display: 'flex' }}>
          <Box sx={{ alignItems: 'baseline', display: 'flex', width: '100%' }}>
            <Button
              disabled={saldo === ''}
              onClick={() => {
                const selected = selectedRow ?? margenFinancieroInformeDefault
                mutateAjusteSaldo({
                  accountingAreaID: selected.accountingAreaID,
                  ajusteDia: ajusteDia ? '0' : '1',
                  ajusteMes: ajusteMes ? '0' : '1',
                  balance: selected.balance,
                  ccy: selected.currencyID,
                  epi: selected.epigrafeID.toString(),
                  fecha: selected.fecha,
                  producto: selected.producto,
                  saldo: selected.saldo.toString(),
                  tradeGroup: selected.tradeGroup
                })
              }}
              sx={{ background: '#E5F1EE', color: 'green', marginRight: '5px' }}
              variant='contained'
            >
              Ajuste Saldo:
            </Button>
            <OutlinedInput
              disabled={selectedRow === null}
              id='my-label-id'
              onChange={e => setSaldo(e.target.value)}
              type='number'
              value={saldo}
            />
          </Box>
          <Box sx={{ alignItems: 'baseline', display: 'flex', width: '100%' }}>
            <Button
              disabled={interes === ''}
              onClick={() => {
                const selected = selectedRow ?? margenFinancieroInformeDefault
                mutateAjusteIntereses({
                  ajusteDia: ajusteDia ? '0' : '1',
                  ajusteMes: ajusteMes ? '0' : '1',
                  area: selected.accountingAreaID,
                  balance: selected.balance,
                  ccy: selected.currencyID,
                  epi: selected.epigrafeID.toString(),
                  fecha: selected.fecha,
                  int: interes,
                  producto: selected.producto,
                  tradeGroup: selected.tradeGroup
                })
              }}
              sx={{ background: '#E5F1EE', color: 'green', marginRight: '5px' }}
              variant='contained'
            >
              Ajuste Intereses:
            </Button>
            <OutlinedInput
              disabled={selectedRow === null}
              id='my-label-id2'
              onChange={e => setInteres(e.target.value)}
              value={interes}
            />
          </Box>
          <Box sx={{ alignItems: 'baseline', display: 'flex', width: '100%' }}>
            <Button
              disabled={margen === ''}
              onClick={() => {
                const selected = selectedRow ?? margenFinancieroInformeDefault
                mutateAjusteMargen({
                  ajusteDia: ajusteDia ? '0' : '1',
                  ajusteMes: ajusteMes ? '0' : '1',
                  area: selected.accountingAreaID,
                  balance: selected.balance,
                  ccy: selected.currencyID,
                  epi: selected.epigrafeID.toString(),
                  fecha: selected.fecha,
                  producto: selected.producto,
                  saldo: selected.saldo.toString(),
                  tradeGroup: selected.tradeGroup
                })
              }}
              sx={{ background: '#E5F1EE', color: 'green', marginRight: '5px' }}
              variant='contained'
            >
              Ajuste Margen:
            </Button>
            <OutlinedInput
              disabled={selectedRow === null}
              id='my-label-id3'
              onChange={e => setMargen(e.target.value)}
              type='number'
              value={margen}
            />
          </Box>
        </Container>
      </Grid>

      <Grid item sx={{ display: 'flex', flexDirection: 'row' }} xs={12}>
        <FormGroup sx={{ ml: '5%' }}>
          <FormControlLabel
            control={
              <Checkbox
                onClick={() => setAjusteDia(!ajusteDia)}
                value={ajusteDia}
              />
            }
            disabled={selectedRow === null}
            label='Ajuste día'
          />
          <FormControlLabel
            control={
              <Checkbox
                onClick={() => setAjusteMes(!ajusteMes)}
                value={ajusteMes}
              />
            }
            disabled={selectedRow === null}
            label='Ajuste mes'
          />
        </FormGroup>
      </Grid>
      {isAjusteSaldoPending ||
        isAjusteInteresesPending ||
        isAjusteMargePending ||
        (isAplicaAjusteMargePending && (
          <Grid item xs={12}>
            <LinearProgress sx={{ mb: '15px' }} />
          </Grid>
        ))}
      <Grid item xs={12}>
        <Paper
          elevation={10}
          sx={{ ...paperStyle, maxHeight: '325px', overflowY: 'auto' }}
        >
          <GenericTable
            data={data}
            entries={margenFinancieroInforme}
            isSelected
            metadata={margenFinancieroEdicionMetadata}
            onRowClick={index =>
              setSelectedRow(nth(index, data) ?? margenFinancieroInformeDefault)
            }
          />
        </Paper>
      </Grid>
      <Grid item xs={12}>
        <Button
          onClick={() => {
            mutateAplicaAjusteMargen({
              fecha: date.format('YYYY-MM-DD'),
              user: user?.email ?? ''
            })
          }}
          sx={{ background: '#E5F1EE', color: 'green', marginRight: '5px' }}
          variant='contained'
        >
          Aplicar Ajustes
        </Button>
      </Grid>
      <Grid item xs={12}>
        <TitleSubtitle title='Ajustes aplicados' />
        <Paper
          elevation={10}
          sx={{ ...paperStyle, maxHeight: '325px', overflowY: 'auto' }}
        >
          {isMargenFinancieroAjustesPending ? (
            <Loading />
          ) : (
            <GenericTable
              data={ajustes}
              entries={margenFinancieroInforme}
              metadata={margenFinancieroAjusteMetadata}
            />
          )}
        </Paper>
      </Grid>
    </Grid>
  )
}
