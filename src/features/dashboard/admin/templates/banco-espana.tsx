/* eslint-disable unicorn/no-keyword-prefix */
import { DateFormControl } from '@d/admin/components/date-form-control'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type BancoEspana,
  type BancoEspanaConverted,
  bancoEspanaConverted,
  bancoEspanaDefault,
  bancoEspanaMetadata
} from '@d/admin/models/banco-espana'
import { BancoEspanaRepository } from '@d/admin/repositories/banco-espana-repository'
import { AppChart } from '@d/common/components/Chart'
import { Fetching } from '@d/common/components/Fetching'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Box, Button, Grid, Paper, Typography } from '@mui/material'
import { useMutation, useQuery } from '@tanstack/react-query'
import dayjs, { type Dayjs } from 'dayjs'
import { nth } from 'rambdax'
import type { FC } from 'react'
import * as Yup from 'yup'

import { alertStore } from '@/common/store/alert-store'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

import { bancoEspanaOpt } from '../data/banco-espana-chart'

export const BancoEspanaTemplate: FC<{ isExceso?: boolean }> = ({
  isExceso = false
}) => {
  const {
    deleteBancoEspana,
    getBancoEspana,
    insertBancoEspana,
    updateBancoEspana
  } = container.get(BancoEspanaRepository)

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)

  const series = useRef<ApexAxisChartSeries>([])
  const selectedData = useRef(bancoEspanaDefault)
  const bancoEspanaConvertedRef = useRef<BancoEspanaConverted[]>([])
  const selectedIndex = useRef<number | null>(0)

  const calcChart = (rows: BancoEspana[]) => {
    series.current = [
      {
        data: rows.map(el =>
          [dayjs(el.fecha).valueOf(), el.saldo ?? 0].slice(0, 31)
        ),
        name: 'fecha'
      }
    ]

    bancoEspanaConvertedRef.current = rows
      .map(el => ({
        ...el,
        tipo: el.tipo?.toFixed(4).replace('.', ',') ?? '0'
      }))
      .slice(0, 31)

    return rows
  }

  const {
    data: bancoEspanaData,
    error: bancoEspanaError,
    isError: isBancoEspanaError,
    isFetching: isBancoEspanaPending,
    refetch: refetchBancoEspana
  } = useQuery({
    queryFn: () => getBancoEspana(isExceso).then(calcChart),
    queryKey: [`getBancoEspana${isExceso ? 'Exceso' : ''}`]
  })

  const {
    isError: isDeleteBancoEspanaError,
    isPending: isDeleteBancoEspanaPending,
    isSuccess: isDeleteBancoEspanaSuccess,
    mutate: deleteAction
  } = useMutation({
    mutationFn: async (isOk: boolean) => {
      setOpenDeleteDialog(false)
      if (!isOk) return
      await deleteBancoEspana(
        (selectedData.current.bdeID ?? 0).toString(),
        isExceso
      )
      void refetchBancoEspana()
    }
  })

  const {
    isError: isSubmitBancoEspanaError,
    isPending: isSubmitBancoEspanaPending,
    isSuccess: isSubmitBancoEspanaSuccess,
    mutate: submitAction
  } = useMutation({
    mutationFn: async (bancoEspana: BancoEspana) => {
      setOpenCreateEditDialog(false)
      await (selectedIndex.current === null
        ? insertBancoEspana(bancoEspana, isExceso)
        : updateBancoEspana(
            (selectedData.current.bdeID ?? 0).toString(),
            bancoEspana,
            isExceso
          ))
      void refetchBancoEspana()
    }
  })

  useEffect(() => {
    void refetchBancoEspana()
  }, [])

  useEffect(() => {
    if (isDeleteBancoEspanaSuccess || isSubmitBancoEspanaSuccess)
      alertStore.displayInfo()
  }, [isDeleteBancoEspanaSuccess, isSubmitBancoEspanaSuccess])

  useEffect(() => {
    if (isSubmitBancoEspanaError || isDeleteBancoEspanaError)
      alertStore.displayError()
  }, [isSubmitBancoEspanaError, isDeleteBancoEspanaError])

  return (
    <Fetching
      errorMessage={bancoEspanaError?.message}
      fullHeight
      isError={isBancoEspanaError}
      isFetching={isBancoEspanaPending}
    >
      <Reload onClick={() => refetchBancoEspana()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = bancoEspanaDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Grid item xs={7}>
        <Paper
          elevation={1}
          sx={{ ...paperStyle, maxHeight: '400px', overflowY: 'auto' }}
        >
          <GenericTable
            data={bancoEspanaConvertedRef.current}
            entries={bancoEspanaConverted}
            isDeleteAction
            isEditAction
            metadata={bancoEspanaMetadata}
            onDeleteClick={index => {
              selectedIndex.current = index
              setOpenDeleteDialog(true)
            }}
            onEditClick={index => {
              selectedIndex.current = index
              selectedData.current =
                nth(index, bancoEspanaData ?? []) ?? bancoEspanaDefault
              setOpenCreateEditDialog(true)
            }}
            tipoPercent
          />
        </Paper>
      </Grid>
      <Grid item xs={5}>
        <Paper elevation={10} sx={paperStyle}>
          <Box height='100%' p={3} width='100%'>
            <Typography variant='h5'>
              Gráfico Banco de España {isExceso ? 'Exceso' : ''}
            </Typography>
            <Box id='chart' sx={{ bgcolor: 'transparent' }}>
              <AppChart
                height={305}
                options={bancoEspanaOpt}
                series={series.current}
                type='area'
              />
            </Box>
          </Box>
        </Paper>
      </Grid>
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <LoadingDialog
        open={isSubmitBancoEspanaPending || isDeleteBancoEspanaPending}
      />
      <DialogForm<BancoEspana>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={(bancoEspana, _) => submitAction(bancoEspana)}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          currencyId: Yup.string().required('Campo Requerido'),
          fecha: Yup.mixed<Dayjs>().required('Campo Requerido')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, setFieldValue, values }) => (
          <>
            <DateFormControl<BancoEspana>
              field='fecha'
              required
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha)}
            />
            <TextFormControl<BancoEspana>
              field='currencyID'
              label='CurrencyID:'
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.currencyID.toString()}
            />
            <TextFormControl<BancoEspana>
              field='saldo'
              label='Saldo:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.saldo?.toString() ?? '0'}
            />
            <TextFormControl<BancoEspana>
              field='tipo'
              label='Tipo:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.tipo?.toString() ?? '0'}
            />
          </>
        )}
      </DialogForm>
    </Fetching>
  )
}
