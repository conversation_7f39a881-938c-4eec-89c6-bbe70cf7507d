/* eslint-disable unicorn/no-keyword-prefix */
import { DateFormControl } from '@d/admin/components/date-form-control'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type Ajustes,
  ajustes,
  ajustesDefault,
  ajustesMetadata
} from '@d/admin/models/ajustes'
import { Fetching } from '@d/common/components/Fetching'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Button, ButtonGroup, Container, Grid, Paper } from '@mui/material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import dayjs from 'dayjs'
import { nth } from 'rambdax'

import { paperStyle } from '~/resources/config/paper'

import { AjusteButtonArrays } from './ajustes-components'
import { ajustesController, validate } from './ajustes-controller'

export default () => {
  const {
    activeArea,
    ajustesData,
    ajustesDate,
    ajustesError,
    day,
    deleteAction,
    isAjustesError,
    isAjustesPending,
    isDeleteAjustesPending,
    isSubmitAjustesPending,
    openCreateEditDialog,
    openDeleteDialog,
    refetchAjustes,
    selectedData,
    selectedIndex,
    setActiveArea,
    setAjustesDate,
    setOpenCreateEditDialog,
    setOpenDeleteDialog,
    submitAction
  } = ajustesController()

  return (
    <Fetching
      errorMessage={ajustesError?.message}
      fullHeight
      isError={isAjustesError}
      isFetching={isAjustesPending}
    >
      <Reload onClick={() => refetchAjustes()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = ajustesDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 4, mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={10}>
            <ButtonGroup variant='outlined'>
              {AjusteButtonArrays(activeArea, setActiveArea)}
            </ButtonGroup>
          </Grid>
          <Grid item xs={2}>
            <LocalizationProvider adapterLocale='es' dateAdapter={AdapterDayjs}>
              <DatePicker
                defaultValue={dayjs(ajustesDate)}
                onChange={newValue =>
                  setAjustesDate(newValue?.format('YYYY-MM-DD') ?? day)
                }
                slotProps={{ textField: { size: 'small' } }}
              />
            </LocalizationProvider>
          </Grid>
          <Grid item xs={12}>
            <Paper elevation={10} sx={paperStyle}>
              <GenericTable
                data={ajustesData ?? []}
                entries={ajustes}
                isDeleteAction
                isEditAction
                metadata={ajustesMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, ajustesData ?? []) ?? ajustesDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <LoadingDialog open={isSubmitAjustesPending || isDeleteAjustesPending} />
      <DialogForm<Ajustes>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={(submit, _) => submitAction(submit)}
        open={openCreateEditDialog}
        validate={validate}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, setFieldValue, values }) => (
          <>
            <DateFormControl<Ajustes>
              field='fecha'
              required
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha)}
            />
            <TextFormControl<Ajustes>
              field='accountingAreaID'
              label='Área Id:'
              onBlur={handleBlur}
              onChange={handleChange}
              required
              value={values.accountingAreaID}
            />
            <TextFormControl<Ajustes>
              field='concepto'
              label='Concepto:'
              onBlur={handleBlur}
              onChange={handleChange}
              required
              value={values.concepto}
            />
            <TextFormControl<Ajustes>
              field='importe'
              label='Importe:'
              onBlur={handleBlur}
              onChange={handleChange}
              required
              type='number'
              value={values.importe.toString()}
            />
          </>
        )}
      </DialogForm>
    </Fetching>
  )
}
