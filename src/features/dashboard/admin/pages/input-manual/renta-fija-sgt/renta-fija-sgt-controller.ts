import {
  type RentafijaSgt,
  rentaFijaSgtDefault
} from '@d/admin/models/rentafija-sgt'
import { RentafijaSGTRepository } from '@d/admin/repositories/rentafija-sgt-repository'
import { useMutation, useQuery } from '@tanstack/react-query'
import dayjs, { type Dayjs } from 'dayjs'
import { prop } from 'rambdax'
import {
  type ChangeEvent,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react'

import { alertStore } from '@/common/store/alert-store'
import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

export const rentaFijaSgtController = () => {
  const day = calendarStore.formattedDay.use()

  const {
    deleteRentaFijaSGT,
    getRentaFijaSGT,
    getRentaFijaSGTGroups,
    insertRentaFijaSGT,
    updateRentaFijaSGT
  } = container.get(RentafijaSGTRepository)

  const selectedData = useRef(rentaFijaSgtDefault)
  const selectedIndex = useRef<number | null>(0)

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)
  const [filterRef, setFilterRef] = useState('')
  const [date, setDate] = useState<Dayjs | null>(dayjs(day))

  const {
    data: rentaFijaSGTData,
    error: rentaFijaSGTError,
    isError: isRentaFijaSGTError,
    isFetching: isRentaFijaSGTPending,
    refetch: refetchRentaFijaSGT
  } = useQuery({
    queryFn: () => getRentaFijaSGT(date?.format('YYYY-MM-DD') ?? day),
    queryKey: ['getRentaFijaSGT']
  })

  const {
    data: rentaFijaSGTGroupsData,
    error: rentaFijaSGTGroupsError,
    isError: isRentaFijaSGTGroupsError,
    isFetching: isRentaFijaSGTGroupsPending,
    refetch: refetchRentaFijaSGTGroups
  } = useQuery({
    queryFn: () =>
      getRentaFijaSGTGroups(date?.format('YYYY-MM-DD') ?? day, filterRef),
    queryKey: ['getRentaFijaSGTGroups']
  })

  const handleRefTextChange = useCallback(
    (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
      setFilterRef(event.currentTarget.value),
    []
  )

  const filterRentaFijaSGTData = useMemo(() => {
    if (filterRef.trim() === '') return rentaFijaSGTData

    return (rentaFijaSGTData ?? []).filter(item => {
      const value = prop('referencia', item)
      return value.toLowerCase().includes(filterRef.toLowerCase())
    })
  }, [filterRef, rentaFijaSGTData, date])

  const {
    isError: isDeleteRentaFijaSGTError,
    isPending: isDeleteRentaFijaSGTPending,
    isSuccess: isDeleteRentaFijaSGTSuccess,
    mutate: deleteAction
  } = useMutation({
    mutationFn: async (isOk: boolean) => {
      setOpenDeleteDialog(false)
      if (!isOk) return
      await deleteRentaFijaSGT(selectedData.current.iD.toString())
      void refetchRentaFijaSGT()
    }
  })

  const {
    isError: isSubmitRentaFijaSGTError,
    isPending: isSubmitRentaFijaSGTPending,
    isSuccess: isSubmitRentaFijaSGTSuccess,
    mutate: submitAction
  } = useMutation({
    mutationFn: async (rt: RentafijaSgt) => {
      setOpenCreateEditDialog(false)
      await (selectedIndex.current === null
        ? insertRentaFijaSGT(rt)
        : updateRentaFijaSGT(rt))
      void refetchRentaFijaSGT()
    }
  })

  const refetch = () => {
    void refetchRentaFijaSGT()
    void refetchRentaFijaSGTGroups()
  }

  useEffect(() => {
    if (filterRef.length === 12 || filterRef.length === 0)
      void refetchRentaFijaSGTGroups()
  }, [filterRef])

  useEffect(() => {
    refetch()
  }, [date])

  useEffect(() => {
    if (isDeleteRentaFijaSGTSuccess || isSubmitRentaFijaSGTSuccess)
      alertStore.displayInfo()
  }, [isDeleteRentaFijaSGTSuccess, isSubmitRentaFijaSGTSuccess])

  useEffect(() => {
    if (isSubmitRentaFijaSGTError || isDeleteRentaFijaSGTError)
      alertStore.displayError()
  }, [isSubmitRentaFijaSGTError, isDeleteRentaFijaSGTError])

  return {
    date,
    deleteAction,
    filterRentaFijaSGTData,
    handleRefTextChange,
    isDeleteRentaFijaSGTPending,
    isRentaFijaSGTError,
    isRentaFijaSGTGroupsError,
    isRentaFijaSGTGroupsPending,
    isRentaFijaSGTPending,
    isSubmitRentaFijaSGTPending,
    openCreateEditDialog,
    openDeleteDialog,
    refetch,
    rentaFijaSGTData,
    rentaFijaSGTError,
    rentaFijaSGTGroupsData,
    rentaFijaSGTGroupsError,
    selectedData,
    selectedIndex,
    setDate,
    setOpenCreateEditDialog,
    setOpenDeleteDialog,
    submitAction
  }
}
