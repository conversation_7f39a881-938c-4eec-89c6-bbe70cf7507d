/* eslint-disable unicorn/no-keyword-prefix */
import { DownOutlined, SearchOutlined } from '@ant-design/icons'
import { DateFormControl } from '@d/admin/components/date-form-control'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type RentafijaSgt,
  rentafijaSgt,
  rentaFijaSgtDefault,
  rentafijaSgtMetadata
} from '@d/admin/models/rentafija-sgt'
import { sgtGroups, sgtGroupsMetadata } from '@d/admin/models/sgt-groups'
import { Fetching } from '@d/common/components/Fetching'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  FormControl,
  Grid,
  InputAdornment,
  OutlinedInput,
  Paper,
  Typography
} from '@mui/material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import dayjs, { type Dayjs } from 'dayjs'
import { nth } from 'rambdax'

import { paperStyle } from '~/resources/config/paper'

import { rentaFijaSgtController } from './renta-fija-sgt-controller'

export default () => {
  const {
    date,
    deleteAction,
    filterRentaFijaSGTData,
    handleRefTextChange,
    isDeleteRentaFijaSGTPending,
    isRentaFijaSGTError,
    isRentaFijaSGTGroupsError,
    isRentaFijaSGTGroupsPending,
    isRentaFijaSGTPending,
    isSubmitRentaFijaSGTPending,
    openCreateEditDialog,
    openDeleteDialog,
    refetch,
    rentaFijaSGTData,
    rentaFijaSGTError,
    rentaFijaSGTGroupsData,
    rentaFijaSGTGroupsError,
    selectedData,
    selectedIndex,
    setDate,
    setOpenCreateEditDialog,
    setOpenDeleteDialog,
    submitAction
  } = rentaFijaSgtController()

  return (
    <>
      {!isRentaFijaSGTPending && (
        <Reload onClick={refetch}>
          <Button
            onClick={() => {
              selectedIndex.current = null
              selectedData.current = rentaFijaSgtDefault
              setOpenCreateEditDialog(true)
            }}
            variant='contained'
          >
            Nuevo
          </Button>
        </Reload>
      )}
      <Grid container spacing={3}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            mt: 2,
            px: 3,
            width: '100%'
          }}
        >
          <Box>
            <FormControl sx={{ width: { md: 224, xs: '100%' } }}>
              <OutlinedInput
                id='my-label-id'
                onChange={handleRefTextChange}
                placeholder='Referencia'
                startAdornment={
                  <InputAdornment position='start'>
                    <SearchOutlined />
                  </InputAdornment>
                }
              />
            </FormControl>
          </Box>
          <Box>
            <LocalizationProvider adapterLocale='es' dateAdapter={AdapterDayjs}>
              <DatePicker<Dayjs>
                defaultValue={date}
                onChange={setDate}
                slotProps={{ textField: { size: 'small' } }}
                value={date}
              />
            </LocalizationProvider>
          </Box>
        </Box>
        <Grid item xs={12}>
          <Typography color='primary' component='h1' variant='h4'>
            Carteras SGT Renta Fija
          </Typography>
          <Fetching
            errorMessage={rentaFijaSGTError?.message}
            isError={isRentaFijaSGTError}
            isFetching={isRentaFijaSGTPending}
          >
            <Paper
              elevation={1}
              sx={{
                ...paperStyle,
                marginTop: '25px',
                maxHeight: '400px',
                overflowY: 'auto'
              }}
            >
              <GenericTable
                data={filterRentaFijaSGTData ?? []}
                entries={rentafijaSgt}
                isDeleteAction
                isEditAction
                metadata={rentafijaSgtMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, rentaFijaSGTData ?? []) ?? rentaFijaSgtDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Fetching>
        </Grid>
        <Grid item xs={12}>
          <Accordion>
            <AccordionSummary
              aria-controls='panel1-content'
              expandIcon={<DownOutlined />}
              id='panel1-header'
            >
              SGT Group
            </AccordionSummary>
            <AccordionDetails>
              <Fetching
                errorMessage={rentaFijaSGTGroupsError?.message}
                isError={isRentaFijaSGTGroupsError}
                isFetching={isRentaFijaSGTGroupsPending}
              >
                <Paper
                  elevation={1}
                  sx={{
                    ...paperStyle,
                    maxHeight: '400px',
                    overflowY: 'auto'
                  }}
                >
                  <GenericTable
                    data={rentaFijaSGTGroupsData ?? []}
                    entries={sgtGroups}
                    metadata={sgtGroupsMetadata}
                  />
                </Paper>
              </Fetching>
            </AccordionDetails>
          </Accordion>
        </Grid>
      </Grid>
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <LoadingDialog
        open={isSubmitRentaFijaSGTPending || isDeleteRentaFijaSGTPending}
      />
      <DialogForm<RentafijaSgt>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={(rt, _) => submitAction(rt)}
        open={openCreateEditDialog}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, setFieldValue, values }) => (
          <>
            <TextFormControl<RentafijaSgt>
              field='cartera'
              label='Cartera:'
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.cartera.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='referencia'
              label='Referencia:'
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.referencia.toString()}
            />
            <DateFormControl<RentafijaSgt>
              field='fecha'
              required
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha)}
            />
            <DateFormControl<RentafijaSgt>
              field='fechaVto'
              required
              setFieldValue={setFieldValue}
              value={dayjs(values.fechaVto)}
            />
            <TextFormControl<RentafijaSgt>
              field='vidaRes'
              label='Vida Res:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.vidaRes.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='nominal'
              label='Nominal:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.nominal.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='efectivoHistor'
              label='Efectivo Histor:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.efectivoHistor.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='vacio1'
              label='Vacio 1:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.vacio1.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='vacio2'
              label='Vacio 2:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.vacio2.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='vacio3'
              label='Vacio 3:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.vacio3.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='interesesPeriodif'
              label='Intereses Periodif:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.interesesPeriodif.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='valoracionRepercutida'
              label='Valoracion Repercutida:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.valoracionRepercutida.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='cuponPrepPeriodific'
              label='Cupon Prep Periodific:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.cuponPrepPeriodific.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='precioCritico'
              label='Precio Crítico:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.precioCritico.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='tIR'
              label='Tir:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.tIR.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='efectivoCritico'
              label='Efectivo Crítico:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.efectivoCritico.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='cuponPrepagado'
              label='Cupon Prepagado:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.cuponPrepagado.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='cuponCorrido'
              label='Cupon Corrido:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.cuponCorrido.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='cuponPeriodificado'
              label='Cupon Periodificado:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.cuponPeriodificado.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='ingresosIntereses'
              label='Ingresos Intereses:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.ingresosIntereses.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='ingresosCupPrepag'
              label='Ingresos Cup Prepag:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.ingresosCupPrepag.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='ingresosCup'
              label='Ingresos Cup:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.ingresosCup.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='valoracionTotal'
              label='Valoracion Total:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.valoracionTotal.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='ingresosTotal'
              label='Ingresos Total:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.ingresosTotal.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='resultadosVentasDia'
              label='Resultados Ventas Dia:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.resultadosVentasDia.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='resultadoVentasTotal'
              label='Resultados Ventas Total:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.resultadoVentasTotal.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='resultadoVentasTotal'
              label='Resultados Ventas Total:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.resultadoVentasTotal.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='resultadosTotal'
              label='Resultados Total:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.resultadosTotal.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='cuponCobradoDia'
              label='Cupon Cobrado Dia:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.cuponCobradoDia.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='retencion'
              label='Retencion:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.retencion.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='duracion'
              label='Duracion:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.duracion.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='durCorr'
              label='DurCorr:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.durCorr.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='sensi'
              label='Sensi:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.sensi.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='intAcc'
              label='Intacc:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.intAcc.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='nominal_Last'
              label='NominalLast:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.nominal_Last.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='efectivoHistor_Last'
              label='Efectivo Histor Last:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.efectivoHistor_Last.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='efectivoCritico_Last'
              label='Efectivo Crítico Last:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.efectivoCritico_Last.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='cuponCorrido_Last'
              label='Cupon Corrido Last:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.cuponCorrido_Last.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='cuponPeriodificado_Last'
              label='Cupon Periodificado Last:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.cuponPeriodificado_Last.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='valoracionTotal_Last'
              label='Valoracion Total Last:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.valoracionTotal_Last.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='valoracionRepercutida_Last'
              label='Valoracion Repercutida Last:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.valoracionRepercutida_Last.toString()}
            />
            <DateFormControl<RentafijaSgt>
              field='fecha_Last'
              required
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha_Last)}
            />
            <TextFormControl<RentafijaSgt>
              field='tipo'
              label='Tipo:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.tipo.toString()}
            />
            <TextFormControl<RentafijaSgt>
              field='cuponCobradoDia_Last'
              label='Cupon Cobrado Dia Last:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.cuponCobradoDia_Last.toString()}
            />
          </>
        )}
      </DialogForm>
    </>
  )
}
