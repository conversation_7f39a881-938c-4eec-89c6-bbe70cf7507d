/* eslint-disable unicorn/no-keyword-prefix */
import {
  ajusteAplicados,
  ajusteAplicadosAnoMetadata,
  ajusteAplicadosMetadata
} from '@d/admin/models/ajustes-aplicados'
import { AjustesAplicadosRepository } from '@d/admin/repositories/ajustes-aplicados-repository'
import { Fetching } from '@d/common/components/Fetching'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { Reload } from '@d/common/components/Reload'
import { FormControlLabel, Paper, Radio, RadioGroup } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useState } from 'react'

import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const day = calendarStore.actualDay.use()

  const { getAjustesAplicados, getAjustesAplicadosAno } = container.get(
    AjustesAplicadosRepository
  )

  const [isAno, setIsAno] = useState(false)

  const {
    data: ajustesAplicadosData,
    error: ajustesAplicadosError,
    isError: isAjustesAplicadosError,
    isFetching: isAjustesAplicadosPending,
    refetch: refetchAjustesAplicados
  } = useQuery({
    queryFn: () =>
      getAjustesAplicados().then(data =>
        data.map(el => ({
          ...el,
          horaAjuste: el.horaAjuste.split('T')[0] ?? ''
        }))
      ),
    queryKey: ['getAjustesAplicados']
  })

  const {
    data: ajustesAplicadosAnoData,
    error: ajustesAplicadosAnoError,
    isError: isAjustesAplicadosAnoError,
    isFetching: isAjustesAplicadosAnoPending,
    refetch: refetchAjustesAplicadosAno
  } = useQuery({
    queryFn: getAjustesAplicadosAno,
    queryKey: ['getAjustesAplicadosAno']
  })

  const refetch = () =>
    isAno ? refetchAjustesAplicadosAno() : refetchAjustesAplicados()

  useEffect(() => {
    void refetch()
  }, [day])

  return (
    <Fetching
      errorMessage={
        ajustesAplicadosError?.message ?? ajustesAplicadosAnoError?.message
      }
      fullHeight
      isError={isAjustesAplicadosError || isAjustesAplicadosAnoError}
      isFetching={isAjustesAplicadosPending || isAjustesAplicadosAnoPending}
    >
      <RadioGroup row sx={{ mb: 2, ml: 2 }}>
        <FormControlLabel
          checked={!isAno}
          control={<Radio />}
          label='Ajustes Aplicados'
          onClick={() => {
            void refetch()
            setIsAno(false)
          }}
          value='ajustesAplicados'
        />
        <FormControlLabel
          checked={isAno}
          control={<Radio />}
          label='Ajustes Aplicados Año'
          onClick={() => {
            void refetch()
            setIsAno(true)
          }}
          value='ajustesAplicadosAno'
        />
      </RadioGroup>
      <Reload onClick={refetch} />
      <Paper
        elevation={1}
        sx={{ ...paperStyle, maxHeight: '400px', overflowY: 'auto' }}
      >
        <Fetching
          errorMessage={
            isAno
              ? ajustesAplicadosAnoError?.message
              : ajustesAplicadosError?.message
          }
          isError={isAno ? isAjustesAplicadosAnoError : isAjustesAplicadosError}
          isFetching={
            isAno ? isAjustesAplicadosAnoPending : isAjustesAplicadosPending
          }
        >
          {isAno ? (
            <GenericTable
              data={ajustesAplicadosAnoData ?? []}
              entries={ajusteAplicados}
              metadata={ajusteAplicadosAnoMetadata}
            />
          ) : (
            <GenericTable
              data={ajustesAplicadosData ?? []}
              entries={ajusteAplicados}
              metadata={ajusteAplicadosMetadata}
            />
          )}
        </Fetching>
      </Paper>
    </Fetching>
  )
}
