/* eslint-disable unicorn/no-keyword-prefix */
import { DateFormControl } from '@d/admin/components/date-form-control'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type OtrosSaldos,
  otrosSaldos,
  otrosSaldosDefault,
  otrosSaldosMetadata
} from '@d/admin/models/otros-saldos'
import { OtrosSaldosRepository } from '@d/admin/repositories/otros-saldos-repository'
import { Fetching } from '@d/common/components/Fetching'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Button, Grid, Paper } from '@mui/material'
import { useMutation, useQuery } from '@tanstack/react-query'
import dayjs, { type Dayjs } from 'dayjs'
import { nth } from 'rambdax'
import { useEffect, useRef, useState } from 'react'
import * as Yup from 'yup'

import { alertStore } from '@/common/store/alert-store'
import { container } from '~/modules/di-module'
import { paperStyle } from '~/resources/config/paper'

export default () => {
  const {
    deleteOtrosSaldos,
    getOtrosSaldos,
    insertOtrosSaldos,
    updateOtrosSaldos
  } = container.get(OtrosSaldosRepository)

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)

  const selectedData = useRef(otrosSaldosDefault)
  const selectedIndex = useRef<number | null>(0)

  const {
    data: otrosSaldosData,
    error: otrosSaldosError,
    isError: isOtrosSaldosError,
    isFetching: isOtrosSaldosPending,
    refetch: refetchOtrosSaldos
  } = useQuery({
    queryFn: getOtrosSaldos,
    queryKey: ['getOtrosSaldos']
  })

  const {
    isError: isDeleteOtrosSaldosError,
    isPending: isDeleteOtrosSaldosPending,
    isSuccess: isDeleteOtrosSaldosSuccess,
    mutate: deleteAction
  } = useMutation({
    mutationFn: async (isOk: boolean) => {
      setOpenDeleteDialog(false)
      if (!isOk) return
      await deleteOtrosSaldos(selectedData.current.osid.toString())
      void refetchOtrosSaldos()
    }
  })

  const {
    isError: isSubmitOtrosSaldosError,
    isPending: isSubmitOtrosSaldosPending,
    isSuccess: isSubmitOtrosSaldosSuccess,
    mutate: submitAction
  } = useMutation({
    mutationFn: async (oo: OtrosSaldos) => {
      setOpenCreateEditDialog(false)
      await (selectedIndex.current === null
        ? insertOtrosSaldos(oo)
        : updateOtrosSaldos(selectedData.current.osid.toString(), oo))
      void refetchOtrosSaldos()
    }
  })

  useEffect(() => {
    if (isDeleteOtrosSaldosSuccess || isSubmitOtrosSaldosSuccess)
      alertStore.displayInfo()
  }, [isDeleteOtrosSaldosSuccess, isSubmitOtrosSaldosSuccess])

  useEffect(() => {
    if (isSubmitOtrosSaldosError || isDeleteOtrosSaldosError)
      alertStore.displayError()
  }, [isSubmitOtrosSaldosError, isDeleteOtrosSaldosError])

  return (
    <Fetching
      errorMessage={otrosSaldosError?.message}
      fullHeight
      isError={isOtrosSaldosError}
      isFetching={isOtrosSaldosPending}
    >
      <Reload onClick={() => refetchOtrosSaldos()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = otrosSaldosDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Grid item xs={12}>
        <Paper
          elevation={1}
          sx={{ ...paperStyle, maxHeight: '400px', overflowY: 'auto' }}
        >
          <GenericTable
            data={otrosSaldosData ?? []}
            entries={otrosSaldos}
            isDeleteAction
            isEditAction
            metadata={otrosSaldosMetadata}
            onDeleteClick={index => {
              selectedIndex.current = index
              setOpenDeleteDialog(true)
            }}
            onEditClick={index => {
              selectedIndex.current = index
              selectedData.current =
                nth(index, otrosSaldosData ?? []) ?? otrosSaldosDefault
              setOpenCreateEditDialog(true)
            }}
            tipoPercent
          />
        </Paper>
      </Grid>
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <LoadingDialog
        open={isSubmitOtrosSaldosPending || isDeleteOtrosSaldosPending}
      />
      <DialogForm<OtrosSaldos>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={(oo, _) => submitAction(oo)}
        open={openCreateEditDialog}
        validationSchema={Yup.object().shape({
          fecha: Yup.mixed<Dayjs>().required('Campo Requerido'),
          producto: Yup.string().required('Campo Requerido')
        })}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, setFieldValue, values }) => (
          <>
            <DateFormControl<OtrosSaldos>
              field='fecha'
              required
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha)}
            />
            <TextFormControl<OtrosSaldos>
              field='evento'
              label='Evento:'
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.evento.toString()}
            />
            <TextFormControl<OtrosSaldos>
              field='saldo'
              label='Saldo:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.saldo.toString()}
            />
            <TextFormControl<OtrosSaldos>
              field='producto'
              label='Producto:'
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.producto.toString()}
            />
            <TextFormControl<OtrosSaldos>
              field='tipo'
              label='Tipo:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.tipo.toString()}
            />
            <TextFormControl<OtrosSaldos>
              field='balance'
              label='Balance:'
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.balance.toString()}
            />
          </>
        )}
      </DialogForm>
    </Fetching>
  )
}
