/* eslint-disable unicorn/no-keyword-prefix */
import { type Pool, poolDefault } from '@d/admin/models/pool'
import { PoolRepository } from '@d/admin/repositories/pool-repository'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useEffect, useRef, useState } from 'react'

import { alertStore } from '@/common/store/alert-store'
import { calendarStore } from '@/common/store/calendar-store'
import { container } from '~/modules/di-module'

export const validate = (pool: Pool) => {
  const errors: Record<string, string> = {}
  if (!pool.fecha) errors['fecha'] = 'Required'
  return errors
}

export const poolController = () => {
  const day = calendarStore.actualDay.use()

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [openCreateEditDialog, setOpenCreateEditDialog] = useState(false)

  const { calcularPool, deletePool, getPool, insertPool, updatePool } =
    container.get(PoolRepository)

  const selectedIndex = useRef<number | null>(null)
  const selectedData = useRef(poolDefault)

  const {
    data: poolData,
    error: poolError,
    isError: isPoolError,
    isFetching: isPoolPending,
    refetch: refetchPool
  } = useQuery({
    queryFn: getPool,
    queryKey: ['getPool']
  })

  const {
    isError: isDeletePoolError,
    isPending: isDeletePoolPending,
    isSuccess: isDeletePoolSuccess,
    mutate: deleteAction
  } = useMutation({
    mutationFn: async (isOk: boolean) => {
      setOpenDeleteDialog(false)
      if (!isOk) return
      await deletePool(selectedData.current.id.toString())
      void refetchPool()
    }
  })

  const {
    isError: isSubmitPoolError,
    isPending: isSubmitPoolPending,
    isSuccess: isSubmitPoolSuccess,
    mutate: submitAction
  } = useMutation({
    mutationFn: async (pool: Pool) => {
      setOpenCreateEditDialog(false)
      await (selectedIndex.current === null
        ? insertPool(pool)
        : updatePool(selectedData.current.id.toString(), pool))
      void refetchPool()
    }
  })

  const {
    isError: isCalcularPoolError,
    isPending: isCalcularPoolPending,
    isSuccess: isCalcularPoolSuccess,
    mutate: calcularPoolAction
  } = useMutation({
    mutationFn: calcularPool,
    onSuccess: () => refetchPool()
  })

  useEffect(() => {
    void refetchPool()
  }, [day])

  useEffect(() => {
    if (isDeletePoolSuccess || isSubmitPoolSuccess || isCalcularPoolSuccess)
      alertStore.displayInfo()
  }, [isDeletePoolSuccess, isSubmitPoolSuccess, isCalcularPoolSuccess])

  useEffect(() => {
    if (isSubmitPoolError || isDeletePoolError || isCalcularPoolError)
      alertStore.displayError()
  }, [isSubmitPoolError, isDeletePoolError, isCalcularPoolError])

  return {
    calcularPoolAction,
    deleteAction,
    isCalcularPoolPending,
    isDeletePoolError,
    isDeletePoolPending,
    isDeletePoolSuccess,
    isPoolError,
    isPoolPending,
    isSubmitPoolError,
    isSubmitPoolPending,
    isSubmitPoolSuccess,
    openCreateEditDialog,
    openDeleteDialog,
    poolData,
    poolError,
    refetchPool,
    selectedData,
    selectedIndex,
    setOpenCreateEditDialog,
    setOpenDeleteDialog,
    submitAction
  }
}
