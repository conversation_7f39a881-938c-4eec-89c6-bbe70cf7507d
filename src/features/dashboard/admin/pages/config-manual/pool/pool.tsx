/* eslint-disable unicorn/no-keyword-prefix */
import { DateFormControl } from '@d/admin/components/date-form-control'
import { DeleteDialog } from '@d/admin/components/delete-dialog'
import { DialogForm } from '@d/admin/components/dialog-form'
import { TextFormControl } from '@d/admin/components/text-form.control'
import {
  type Pool,
  pool,
  poolDefault,
  poolMetadata
} from '@d/admin/models/pool'
import { GenericTable } from '@d/common/components/generic-table/GenericTable'
import { LoadingDialog } from '@d/common/components/LoadingDialog'
import { Reload } from '@d/common/components/Reload'
import { Box, Button, Container, Grid, Paper, Typography } from '@mui/material'
import dayjs from 'dayjs'
import { nth } from 'rambdax'

import { Fetching } from '@/dashboard/common/components/Fetching'
import { paperStyle } from '~/resources/config/paper'

import { poolController, validate } from './pool-controller'

export default () => {
  const {
    calcularPoolAction,
    deleteAction,
    isCalcularPoolPending,
    isDeletePoolPending,
    isPoolError,
    isPoolPending,
    isSubmitPoolPending,
    openCreateEditDialog,
    openDeleteDialog,
    poolData,
    poolError,
    refetchPool,
    selectedData,
    selectedIndex,
    setOpenCreateEditDialog,
    setOpenDeleteDialog,
    submitAction
  } = poolController()

  return (
    <Fetching
      errorMessage={poolError?.message}
      fullHeight
      isError={isPoolError}
      isFetching={isPoolPending}
    >
      <Reload onClick={() => refetchPool()}>
        <Button
          onClick={() => {
            selectedIndex.current = null
            selectedData.current = poolDefault
            setOpenCreateEditDialog(true)
          }}
          variant='contained'
        >
          Nuevo
        </Button>
      </Reload>
      <Container maxWidth={false} sx={{ mb: 3, mt: 3 }}>
        <Box
          sx={{
            alignItems: 'center',
            display: 'flex',
            justifyContent: 'space-between',
            mb: 3,
            px: '20px',
            width: '100%'
          }}
        >
          <Typography sx={{ marginRight: '10px' }} variant='subtitle1'>
            Depósitos FTPOLTED Depo Día Ajuste Pool ATAS POOL
          </Typography>
          <Button
            onClick={() => calcularPoolAction()}
            sx={{ background: '#E5F1EE', color: 'green', marginRight: '5px' }}
            variant='contained'
          >
            Calcular Pool
          </Button>
        </Box>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper elevation={10} sx={paperStyle}>
              <GenericTable
                data={poolData ?? []}
                entries={pool}
                isDeleteAction
                isEditAction
                metadata={poolMetadata}
                onDeleteClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, poolData ?? []) ?? poolDefault
                  setOpenDeleteDialog(true)
                }}
                onEditClick={index => {
                  selectedIndex.current = index
                  selectedData.current =
                    nth(index, poolData ?? []) ?? poolDefault
                  setOpenCreateEditDialog(true)
                }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Container>
      <DeleteDialog onClick={deleteAction} open={openDeleteDialog} />
      <LoadingDialog
        open={
          isSubmitPoolPending || isDeletePoolPending || isCalcularPoolPending
        }
      />
      <DialogForm<Pool>
        onClose={() => setOpenCreateEditDialog(false)}
        onSubmit={(pool, _) => submitAction(pool)}
        open={openCreateEditDialog}
        validate={validate}
        values={selectedData.current}
      >
        {({ handleBlur, handleChange, setFieldValue, values }) => (
          <>
            <DateFormControl<Pool>
              field='fecha'
              required
              setFieldValue={setFieldValue}
              value={dayjs(values.fecha)}
            />
            <TextFormControl<Pool>
              field='saldo'
              label='Saldo:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.saldo.toString()}
            />
            <TextFormControl<Pool>
              field='tipo'
              label='Tipo:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.tipo?.toString() ?? '0'}
            />
            <TextFormControl<Pool>
              field='totalDepo'
              label='Total:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.totalDepo?.toString() ?? '0'}
            />
            <TextFormControl<Pool>
              field='ajustePool'
              label='Ajuste:'
              onBlur={handleBlur}
              onChange={handleChange}
              type='number'
              value={values.ajustePool?.toString() ?? '0'}
            />
          </>
        )}
      </DialogForm>
    </Fetching>
  )
}
